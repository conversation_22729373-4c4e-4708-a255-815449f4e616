import { RowData } from './RenderOutsourceTable';
type UseOutsourceLabourReturn = {
    outsourceYears: number[];
    selectedOutsourceYears: number[];
    outsourceRowData: RowData[][];
    addOutsourceTable: () => void;
    deleteSelectedOutsourceTables: () => void;
    toggleOutsourceYear: (year: number) => void;
    handleOutsourceMonthChange: (yearIdx: number, rowIdx: number, monthIdx: number, value: string) => void;
};
declare const useOutsourceLabour: (curYear: number) => UseOutsourceLabourReturn;
export default useOutsourceLabour;
//# sourceMappingURL=useOutsourceLabour.d.ts.map