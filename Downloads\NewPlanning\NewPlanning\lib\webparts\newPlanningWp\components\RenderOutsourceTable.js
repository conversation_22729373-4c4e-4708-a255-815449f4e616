var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
import React from "react";
var OutsourceLabourTable = function (_a) {
    var outsourceYears = _a.outsourceYears, selectedOutsourceYears = _a.selectedOutsourceYears, outsourceRowData = _a.outsourceRowData, addOutsourceTable = _a.addOutsourceTable, deleteSelectedOutsourceTables = _a.deleteSelectedOutsourceTables, toggleOutsourceYear = _a.toggleOutsourceYear, handleOutsourceMonthChange = _a.handleOutsourceMonthChange;
    // Simple inline styles for headers and sticky columns
    var thStyle = {
        border: "1px solid #ccc",
        whiteSpace: "nowrap",
        textAlign: "center",
        backgroundColor: "#f0f0f0",
    };
    var stickyThStyle = __assign(__assign({}, thStyle), { position: "sticky", left: 0, backgroundColor: "#f0f0f0", zIndex: 2 });
    var stickyTdStyle = {
        position: "sticky",
        left: 0,
        backgroundColor: "#fff",
        zIndex: 1,
    };
    var stickyLeft1 = __assign(__assign({}, stickyThStyle), { left: 0, zIndex: 3 });
    var stickyLeft2 = __assign(__assign({}, stickyThStyle), { left: 45, zIndex: 3 });
    return (React.createElement("div", null,
        outsourceYears.map(function (year, idx) {
            var _a, _b, _c, _d, _e, _f;
            return (React.createElement("div", { key: year, style: { marginBottom: 40, padding: 20, border: "1px solid #ccc" } },
                React.createElement("div", { style: { overflowX: "auto", marginTop: 10 } },
                    React.createElement("table", { style: { borderCollapse: "collapse", width: "100%" } },
                        React.createElement("thead", null,
                            idx !== 0 && (React.createElement("tr", null,
                                React.createElement("th", { style: stickyLeft1 },
                                    React.createElement("input", { type: "checkbox", id: "chkTable_".concat(year), className: "deleteTable", checked: selectedOutsourceYears.includes(year), onChange: function () { return toggleOutsourceYear(year); } })),
                                React.createElement("th", { style: stickyLeft2 }, "Select the checkbox to delete entire table"))),
                            React.createElement("tr", null,
                                React.createElement("th", { style: stickyLeft1 }, "Year"),
                                React.createElement("th", { style: stickyLeft2 }, "Total Year Plan (T-JPY)"),
                                React.createElement("th", { style: stickyThStyle }, "Total Year FTE (Numbers)")),
                            React.createElement("tr", null,
                                React.createElement("td", { style: stickyLeft1 }, year),
                                React.createElement("td", { style: stickyLeft2 }, ((_c = (_b = (_a = outsourceRowData[idx]) === null || _a === void 0 ? void 0 : _a.find(function (r) { return r.category === "FTE Budget Plan (T-JPY)"; })) === null || _b === void 0 ? void 0 : _b.yearSummary) === null || _c === void 0 ? void 0 : _c.plan.toFixed(2)) || "0"),
                                React.createElement("td", { style: stickyThStyle }, ((_f = (_e = (_d = outsourceRowData[idx]) === null || _d === void 0 ? void 0 : _d.find(function (r) { return r.category === "FTE Budget Plan (T-JPY)"; })) === null || _e === void 0 ? void 0 : _e.yearSummary) === null || _f === void 0 ? void 0 : _f.fte.toFixed(2)) || "0")),
                            React.createElement("tr", null,
                                React.createElement("th", { style: stickyLeft1 }, "Year"),
                                React.createElement("th", { style: stickyLeft2 }, "FTE Plan vs. Actual"),
                                ["JAN", "FEB", "MAR", "APR", "MAY", "JUN", "JUL", "AUG", "SEP", "OCT", "NOV", "DEC"].map(function (m) { return (React.createElement("th", { key: m, style: stickyThStyle }, m)); }),
                                React.createElement("th", { style: stickyThStyle }, "OP"),
                                React.createElement("th", { style: stickyThStyle }, "EA1"),
                                React.createElement("th", { style: stickyThStyle }, "EA2"),
                                React.createElement("th", { style: stickyThStyle }, "Year-End"))),
                        React.createElement("tbody", null, ["FTE Plan (Numbers)", "FTE Budget Plan (T-JPY)", "FTE Budget Actual (T-JPY)"].map(function (category, catIdx) {
                            var _a, _b, _c, _d, _e, _f, _g, _h;
                            return (React.createElement("tr", { key: catIdx },
                                React.createElement("td", { style: stickyTdStyle }, year),
                                React.createElement("td", { style: __assign(__assign({}, stickyTdStyle), { left: 45 }) },
                                    React.createElement("input", { type: "text", value: category, disabled: true, style: { border: "none", background: "transparent", width: "100%" } })),
                                Array.from({ length: 12 }).map(function (_, monthIdx) {
                                    var _a, _b;
                                    return (React.createElement("td", { key: monthIdx },
                                        React.createElement("input", { type: "text", value: ((_b = (_a = outsourceRowData[idx]) === null || _a === void 0 ? void 0 : _a[catIdx]) === null || _b === void 0 ? void 0 : _b.months[monthIdx]) || "", onChange: function (e) { return handleOutsourceMonthChange(idx, catIdx, monthIdx, e.target.value); } })));
                                }),
                                React.createElement("td", null,
                                    React.createElement("input", { type: "text", disabled: true, value: ((_b = (_a = outsourceRowData[idx]) === null || _a === void 0 ? void 0 : _a[catIdx]) === null || _b === void 0 ? void 0 : _b.totals.op) || "" })),
                                React.createElement("td", null,
                                    React.createElement("input", { type: "text", disabled: true, value: ((_d = (_c = outsourceRowData[idx]) === null || _c === void 0 ? void 0 : _c[catIdx]) === null || _d === void 0 ? void 0 : _d.totals.ea1) || "" })),
                                React.createElement("td", null,
                                    React.createElement("input", { type: "text", disabled: true, value: ((_f = (_e = outsourceRowData[idx]) === null || _e === void 0 ? void 0 : _e[catIdx]) === null || _f === void 0 ? void 0 : _f.totals.ea2) || "" })),
                                React.createElement("td", null,
                                    React.createElement("input", { type: "text", disabled: true, value: ((_h = (_g = outsourceRowData[idx]) === null || _g === void 0 ? void 0 : _g[catIdx]) === null || _h === void 0 ? void 0 : _h.totals.yearEnd) || "" }))));
                        }))))));
        }),
        React.createElement("div", { style: { textAlign: "right", marginTop: 20 } },
            React.createElement("button", { onClick: addOutsourceTable, style: { marginRight: 10, padding: "6px 12px", backgroundColor: "#0078d4", color: "#fff" } }, "Add"),
            React.createElement("button", { onClick: deleteSelectedOutsourceTables, disabled: selectedOutsourceYears.length === 0, style: {
                    padding: "6px 12px",
                    backgroundColor: selectedOutsourceYears.length === 0 ? "#aaa" : "#a80000",
                    color: "#fff",
                    cursor: selectedOutsourceYears.length === 0 ? "not-allowed" : "pointer",
                } }, "Delete"))));
};
export default OutsourceLabourTable;
//# sourceMappingURL=RenderOutsourceTable.js.map