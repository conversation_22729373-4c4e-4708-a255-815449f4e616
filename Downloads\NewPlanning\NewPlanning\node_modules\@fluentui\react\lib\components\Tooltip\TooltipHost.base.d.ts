import * as React from 'react';
import { TooltipDelay } from './Tooltip.types';
import type { ITooltipHostProps, ITooltipHost } from './TooltipHost.types';
export interface ITooltipHostState {
    /** @deprecated No longer used internally */
    isAriaPlaceholderRendered: boolean;
    isTooltipVisible: boolean;
}
export declare class TooltipHostBase extends React.Component<ITooltipHostProps, ITooltipHostState> implements ITooltipHost {
    static defaultProps: {
        delay: TooltipDelay;
    };
    static contextType: React.Context<import("@fluentui/react-window-provider").WindowProviderProps>;
    private static _currentVisibleTooltip;
    context: any;
    private _tooltipHost;
    private _classNames;
    private _async;
    private _dismissTimerId;
    private _openTimerId;
    private _defaultTooltipId;
    private _ignoreNextFocusEvent;
    constructor(props: ITooltipHostProps);
    render(): JSX.Element;
    componentDidMount(): void;
    componentWillUnmount(): void;
    show: () => void;
    dismiss: () => void;
    private _getTargetElement;
    private _onTooltipFocus;
    private _onTooltipContentFocus;
    private _onTooltipBlur;
    private _onTooltipMouseEnter;
    private _onTooltipMouseLeave;
    private _onTooltipKeyDown;
    private _clearDismissTimer;
    private _clearOpenTimer;
    private _hideTooltip;
    private _toggleTooltip;
    private _getDelayTime;
}
