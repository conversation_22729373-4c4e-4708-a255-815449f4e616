import React from "react";
export type RowData = {
    category: string;
    months: string[];
    totals: {
        op: string;
        ea1: string;
        ea2: string;
        yearEnd: string;
    };
    yearSummary?: {
        plan: number;
        fte: number;
    };
};
type OutsourceLabourTableProps = {
    outsourceYears: number[];
    selectedOutsourceYears: number[];
    outsourceRowData: RowData[][];
    addOutsourceTable: () => void;
    deleteSelectedOutsourceTables: () => void;
    toggleOutsourceYear: (year: number) => void;
    handleOutsourceMonthChange: (yearIdx: number, rowIdx: number, monthIdx: number, value: string) => void;
};
declare const OutsourceLabourTable: React.FC<OutsourceLabourTableProps>;
export default OutsourceLabourTable;
//# sourceMappingURL=RenderOutsourceTable.d.ts.map