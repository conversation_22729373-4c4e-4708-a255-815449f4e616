import React from "react";
interface MonthTotals {
    op?: string;
    ea1?: string;
    ea2?: string;
    yearEnd?: string;
    total?: string;
}
interface Row {
    spendingLevel: string;
    months: string[];
    totals: MonthTotals;
}
interface YearData {
    year: number;
    rows: Row[];
}
interface MaterialTableProps {
    index: number;
    tblCounter: number;
    curYear: number;
    createMaterialTable: () => void;
    deleteSelectedTables: () => void;
    selectedYears: number[];
    toggleYearSelection: (year: number) => void;
    tableYears: number[];
    rowData: YearData[];
    spendingLevels: string[];
    editableLevels: Set<string>;
    handleMonthChange: (yearIdx: number, rowIdx: number, monthIdx: number, value: string) => void;
    isModal: boolean;
    onUpdate?: () => void;
    onCancel?: () => void;
}
declare const MaterialTable: React.FC<MaterialTableProps>;
export default MaterialTable;
//# sourceMappingURL=Material.d.ts.map