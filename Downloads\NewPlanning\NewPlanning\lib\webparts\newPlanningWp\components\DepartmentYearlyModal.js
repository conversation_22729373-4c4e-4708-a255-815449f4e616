var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
import React from "react";
import DepartmentTable from "./Material";
var overlayStyles = {
    position: "fixed",
    top: 0,
    left: 0,
    width: "100vw",
    height: "100vh",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    zIndex: 9999,
};
var modalStyles = {
    backgroundColor: "#fff",
    padding: "20px",
    borderRadius: "8px",
    width: "80%",
    maxHeight: "80vh",
    overflowY: "auto",
    boxShadow: "0 2px 10px rgba(0,0,0,0.2)"
};
var YearlyDetailModal = function (_a) {
    var yearBlock = _a.yearBlock, onClose = _a.onClose, spendingLevels = _a.spendingLevels;
    var _b = React.useState([]), editableData = _b[0], setEditableData = _b[1];
    React.useEffect(function () {
        if (yearBlock) {
            var monthOrder_1 = [
                "jan", "feb", "mar", "apr", "may", "jun",
                "jul", "aug", "sep", "oct", "nov", "dec"
            ];
            var initialData = [
                {
                    year: Number(yearBlock.key),
                    rows: yearBlock.value.map(function (entry) {
                        var _a, _b, _c, _d, _e;
                        var months = monthOrder_1.map(function (m) {
                            var val = entry[m];
                            return val !== undefined && val !== null && val !== 0 ? String(val) : "";
                        });
                        return {
                            spendingLevel: entry.Type,
                            months: months,
                            totals: {
                                op: String((_a = entry.op) !== null && _a !== void 0 ? _a : ""),
                                ea1: String((_b = entry.ea1) !== null && _b !== void 0 ? _b : ""),
                                ea2: String((_c = entry.ea2) !== null && _c !== void 0 ? _c : ""),
                                yearEnd: String((_d = entry.yearEnd) !== null && _d !== void 0 ? _d : ""),
                                total: String((_e = entry.initialJPY) !== null && _e !== void 0 ? _e : ""),
                            },
                        };
                    }),
                },
            ];
            setEditableData(initialData);
        }
    }, [yearBlock]);
    var handleMonthChange = function (yearIdx, rowIdx, monthIdx, value) {
        setEditableData(function (prev) {
            return prev.map(function (yearData, yIdx) {
                if (yIdx !== yearIdx)
                    return yearData;
                var updatedRows = yearData.rows.map(function (row, rIdx) {
                    if (rIdx !== rowIdx)
                        return row;
                    var updatedMonths = __spreadArray([], row.months, true);
                    updatedMonths[monthIdx] = value;
                    return __assign(__assign({}, row), { months: updatedMonths });
                });
                return __assign(__assign({}, yearData), { rows: updatedRows });
            });
        });
    };
    return (React.createElement("div", { style: overlayStyles },
        React.createElement("div", { style: modalStyles },
            React.createElement("h3", null,
                "Yearly Plan Detail for ",
                yearBlock.key),
            React.createElement(DepartmentTable, { index: 0, tblCounter: 1, curYear: Number(yearBlock.key), createMaterialTable: function () { }, deleteSelectedTables: function () { }, selectedYears: [], toggleYearSelection: function () { }, tableYears: [Number(yearBlock.key)], rowData: editableData, spendingLevels: spendingLevels, editableLevels: new Set(spendingLevels.filter(function (level) { return level.includes("Plan"); })), handleMonthChange: handleMonthChange, isModal: true }),
            React.createElement("div", { style: { marginTop: "20px", textAlign: "right" } },
                React.createElement("button", { onClick: onClose, style: {
                        position: "absolute",
                        top: "10px",
                        right: "10px",
                        backgroundColor: "#a00",
                        color: "#fff",
                        border: "none",
                        padding: "6px 12px",
                        borderRadius: "4px",
                        cursor: "pointer",
                        zIndex: 1,
                    } }, "Close")))));
};
export default YearlyDetailModal;
//# sourceMappingURL=DepartmentYearlyModal.js.map