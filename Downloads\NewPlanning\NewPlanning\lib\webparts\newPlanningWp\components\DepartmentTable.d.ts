import React from "react";
interface MonthTotals {
    op?: string;
    ea1?: string;
    ea2?: string;
    yearEnd?: string;
    total?: string;
}
interface Row {
    spendingLevel: string;
    months: string[];
    totals: MonthTotals;
}
interface YearData {
    year: number;
    rows: Row[];
}
interface DepartmentTableProps {
    index: number;
    tblCounter: number;
    curYear: number;
    createDepartmentTable: () => void;
    deleteSelectedTables: () => void;
    selectedYears: number[];
    toggleYearSelection: (year: number) => void;
    tableYears: number[];
    rowData: YearData[];
    spendingLevels: string[];
    editableLevels: Set<string>;
    handleMonthChange: (yearIdx: number, rowIdx: number, monthIdx: number, value: string) => void;
    onUpdateTable?: () => void;
}
declare const DepartmentTable: React.FC<DepartmentTableProps>;
export default DepartmentTable;
//# sourceMappingURL=DepartmentTable.d.ts.map