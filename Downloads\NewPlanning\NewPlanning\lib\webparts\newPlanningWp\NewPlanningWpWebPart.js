var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
import * as React from 'react';
import * as ReactDom from 'react-dom';
import { Version } from '@microsoft/sp-core-library';
import { PropertyPaneTextField } from '@microsoft/sp-property-pane';
import { BaseClientSideWebPart } from '@microsoft/sp-webpart-base';
import * as strings from 'NewPlanningWpWebPartStrings';
import NewPlanningWp from './components/NewPlanningWp';
var NewPlanningWpWebPart = /** @class */ (function (_super) {
    __extends(NewPlanningWpWebPart, _super);
    function NewPlanningWpWebPart() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this._isDarkTheme = false;
        _this._environmentMessage = '';
        return _this;
    }
    NewPlanningWpWebPart.prototype.render = function () {
        var element = React.createElement(NewPlanningWp, {
            description: this.properties.description,
            isDarkTheme: this._isDarkTheme,
            environmentMessage: this._environmentMessage,
            hasTeamsContext: !!this.context.sdks.microsoftTeams,
            userDisplayName: this.context.pageContext.user.displayName,
            context: this.context,
        });
        ReactDom.render(element, this.domElement);
    };
    NewPlanningWpWebPart.prototype.onInit = function () {
        var _this = this;
        return this._getEnvironmentMessage().then(function (message) {
            _this._environmentMessage = message;
            var style = document.createElement("style");
            style.innerHTML = "\n      @media screen and (min-width: 1024px) {\n        .c_b_cb6f7c2e:not(.g_b_cb6f7c2e) .j_b_cb6f7c2e {\n          display: block !important;\n          max-width: 1600px !important;\n        }\n      }\n\n      .CanvasZone {\n        padding: 0 !important;\n        max-width: 1600px !important;\n        margin: 0 auto !important;\n      }\n\n      .CanvasSection {\n        padding: 0 !important;\n      }\n\n      .ControlZone .ControlZone--clean .a_a_50a7110f {\n        margin: 0 !important;\n        padding: 0 !important;\n      }\n\n      #workbenchPageContent {\n        max-width: none !important;\n        width: 100% !important;\n      }\n\n      #sp-appBar,\n      #spCommandBar,\n      #SuiteNavWrapper,\n      .commandBarButtonHeightAndColor[aria-label=\"Command bar\"],\n      .headerRow-50,\n      .j_m_4ade22aa#CommentsWrapper {\n        display: none !important;\n      }\n\n      .p_e_8474018e {\n        padding: 0 !important;\n      }\n\n      .a_g_cb6f7c2e:not(.e_g_cb6f7c2e):not(.aa_g_cb6f7c2e) {\n        margin: 0 !important;\n        padding: 0 !important;\n      }\n\n      @media screen and (min-width: 1024px) {\n        .a_a_cb6f7c2e:not(.e_a_cb6f7c2e) .h_a_cb6f7c2e {\n          max-width: 100% !important;\n        }\n      }\n        .m_b_d71df89c {\n        overflow-y: hidden !important;\n      }\n    ";
            // Defer style append to ensure DOM elements exist
            document.head.appendChild(style);
        });
    };
    NewPlanningWpWebPart.prototype._getEnvironmentMessage = function () {
        var _this = this;
        if (!!this.context.sdks.microsoftTeams) { // running in Teams, office.com or Outlook
            return this.context.sdks.microsoftTeams.teamsJs.app.getContext()
                .then(function (context) {
                var environmentMessage = '';
                switch (context.app.host.name) {
                    case 'Office': // running in Office
                        environmentMessage = _this.context.isServedFromLocalhost ? strings.AppLocalEnvironmentOffice : strings.AppOfficeEnvironment;
                        break;
                    case 'Outlook': // running in Outlook
                        environmentMessage = _this.context.isServedFromLocalhost ? strings.AppLocalEnvironmentOutlook : strings.AppOutlookEnvironment;
                        break;
                    case 'Teams': // running in Teams
                    case 'TeamsModern':
                        environmentMessage = _this.context.isServedFromLocalhost ? strings.AppLocalEnvironmentTeams : strings.AppTeamsTabEnvironment;
                        break;
                    default:
                        environmentMessage = strings.UnknownEnvironment;
                }
                return environmentMessage;
            });
        }
        return Promise.resolve(this.context.isServedFromLocalhost ? strings.AppLocalEnvironmentSharePoint : strings.AppSharePointEnvironment);
    };
    NewPlanningWpWebPart.prototype.onThemeChanged = function (currentTheme) {
        if (!currentTheme) {
            return;
        }
        this._isDarkTheme = !!currentTheme.isInverted;
        var semanticColors = currentTheme.semanticColors;
        if (semanticColors) {
            this.domElement.style.setProperty('--bodyText', semanticColors.bodyText || null);
            this.domElement.style.setProperty('--link', semanticColors.link || null);
            this.domElement.style.setProperty('--linkHovered', semanticColors.linkHovered || null);
        }
    };
    NewPlanningWpWebPart.prototype.onDispose = function () {
        ReactDom.unmountComponentAtNode(this.domElement);
    };
    Object.defineProperty(NewPlanningWpWebPart.prototype, "dataVersion", {
        get: function () {
            return Version.parse('1.0');
        },
        enumerable: false,
        configurable: true
    });
    NewPlanningWpWebPart.prototype.getPropertyPaneConfiguration = function () {
        return {
            pages: [
                {
                    header: {
                        description: strings.PropertyPaneDescription
                    },
                    groups: [
                        {
                            groupName: strings.BasicGroupName,
                            groupFields: [
                                PropertyPaneTextField('description', {
                                    label: strings.DescriptionFieldLabel
                                })
                            ]
                        }
                    ]
                }
            ]
        };
    };
    return NewPlanningWpWebPart;
}(BaseClientSideWebPart));
export default NewPlanningWpWebPart;
//# sourceMappingURL=NewPlanningWpWebPart.js.map