import React from "react";
interface Totals {
    op: string;
    ea1: string;
    ea2: string;
    yearEnd: string;
    total: string;
}
interface LabourRow {
    category: string;
    months: string[];
    totals: Totals;
    labourRate?: number;
}
interface LabourYearData {
    year: number;
    rows: LabourRow[];
    totalPlanHours?: number;
}
interface InternalLabourTableProps {
    labourData: LabourYearData[];
    onDataChange: (updatedData: LabourYearData[]) => void;
    selectedYears: number[];
    toggleYearSelection: (year: number) => void;
    addYear: () => void;
    deleteYears: () => void;
    calculateTotalsByQuarter: (months: string[], rate: number) => Totals;
    department: string;
    isModal: boolean;
    onUpdate?: () => void;
    onCancel?: () => void;
}
declare const _default: React.NamedExoticComponent<InternalLabourTableProps>;
export default _default;
//# sourceMappingURL=InternalLabourTable.d.ts.map