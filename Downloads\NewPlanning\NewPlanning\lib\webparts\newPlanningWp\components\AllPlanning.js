var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
import * as React from 'react';
import styles from './NewPlanningWp.module.scss';
import { Icon, initializeIcons } from '@fluentui/react';
import { sp } from "@pnp/sp/presets/all";
initializeIcons();
var menuItems = [
    { key: 'home', text: 'Home', icon: 'Home', href: '#home' },
    { key: 'newplanning', text: 'New Planning', icon: 'AddTo', href: '#newplanning' },
    { key: 'mgrplanning', text: 'Mgr. Planning', icon: 'People', href: '#mgrplanning' },
    { key: 'allplanning', text: 'All Planning', icon: 'ViewAll', href: '#allplanning' },
    { key: 'splapproval', text: 'SPL Approval', icon: 'CheckMark', href: '#splapproval' },
    { key: 'budgetapproval', text: 'Budget Approval', icon: 'Money', href: '#budgetapproval' },
    { key: 'report', text: 'Report', icon: 'ReportDocument', href: '#report' },
    { key: 'masterlist', text: 'Master List', icon: 'List', href: '#masterlist' },
    { key: 'administration', text: 'Administration', icon: 'Settings', href: '#administration' },
    { key: 'chatbox', text: 'ChatBox', icon: 'Chat', href: '#chatbox' },
];
var AllPlanningWp = function () {
    sp.setup({
        sp: {
            baseUrl: "https://corptb.sharepoint.com/sites/apac-10747/"
        }
    });
    // usage
    var _a = React.useState('newplanning'), activeKey = _a[0], setActiveKey = _a[1];
    var handleClick = function (key) {
        setActiveKey(key);
    };
    var _b = React.useState('Loading...'), userName = _b[0], setUserName = _b[1];
    var _c = React.useState(null), profilePhoto = _c[0], setProfilePhoto = _c[1];
    React.useEffect(function () {
        var fetchUserInfo = function () { return __awaiter(void 0, void 0, void 0, function () {
            var currentUser, userProfile, pictureUrlProp, error_1;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 3, , 4]);
                        return [4 /*yield*/, sp.web.currentUser.get()];
                    case 1:
                        currentUser = _a.sent();
                        return [4 /*yield*/, sp.profiles.myProperties.get()];
                    case 2:
                        userProfile = _a.sent();
                        setUserName(currentUser.Title);
                        pictureUrlProp = userProfile.UserProfileProperties.find(function (prop) { return prop.Key === "PictureURL"; });
                        if (pictureUrlProp && pictureUrlProp.Value) {
                            setProfilePhoto(pictureUrlProp.Value);
                        }
                        else {
                            setProfilePhoto(null); // fallback
                        }
                        return [3 /*break*/, 4];
                    case 3:
                        error_1 = _a.sent();
                        console.error("Error fetching user profile info", error_1);
                        return [3 /*break*/, 4];
                    case 4: return [2 /*return*/];
                }
            });
        }); };
        fetchUserInfo().catch(function (error) {
            console.error("Unhandled error in fetchUserInfo", error);
        });
    }, []);
    return (React.createElement(React.Fragment, null,
        React.createElement("div", { className: styles.headerContainer },
            React.createElement("a", { href: "https://corptb.sharepoint.com/sites/apac-10747/SitePages/budgetController.aspx", className: styles.logoLink, "aria-label": "Go to Home" },
                React.createElement("img", { src: "https://corptb.sharepoint.com/sites/apac-10747/SiteAssets/BudgetV1/assets/img/fuso-logo.svg", alt: "Logo", className: styles.logoImg })),
            React.createElement("span", { className: styles.titleText }, "R&D Budget Management Database"),
            React.createElement("div", { className: styles.userInfo },
                React.createElement("div", { className: styles.avatar }, profilePhoto ? (React.createElement("img", { src: profilePhoto, alt: "Profile", className: styles.profileImg })) : (React.createElement("i", { className: "bi bi-person-circle" }))),
                React.createElement("label", { id: "userName", className: styles.userNameLabel }, userName))),
        React.createElement("div", { className: styles.navbar },
            React.createElement("div", { className: styles.leftMenu }, menuItems.map(function (item) { return (React.createElement("a", { key: item.key, href: item.href, className: "".concat(styles.navItem, " ").concat(activeKey === item.key ? styles.activeNavItem : ''), onClick: function () { return handleClick(item.key); } },
                React.createElement(Icon, { iconName: item.icon, styles: { root: { marginRight: 6, fontSize: 16 } } }),
                item.text)); })),
            React.createElement("div", { className: styles.rightMenu },
                React.createElement("a", { href: "#logout", className: styles.navItem },
                    React.createElement(Icon, { iconName: "SignOut", styles: { root: { marginRight: 6, fontSize: 16 } } }),
                    "Logout")))));
};
export default AllPlanningWp;
//# sourceMappingURL=AllPlanning.js.map